//+------------------------------------------------------------------+
//|                                            BeastEU_Utils.mqh     |
//|                     Smart Money Concepts - Utility Functions     |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//+------------------------------------------------------------------+
#ifndef BEASTEU_UTILS_MQH
#define BEASTEU_UTILS_MQH

#include "BeastEU_Common.mqh"

//-----------------------------------------------------------------------//
// Global Variables for Utilities
//-----------------------------------------------------------------------//
SAnalysisSettings g_settings;
datetime g_last_update = 0;
int g_total_objects = 0;

//-----------------------------------------------------------------------//
// Logging Functions
//-----------------------------------------------------------------------//
void LogMessage(string message) {
    if(g_settings.enable_logging) {
        string timestamp = TimeToString(TimeTradeServer(), TIME_DATE|TIME_SECONDS);
        Print("[", timestamp, "] SMC: ", message);
    }
}

void LogError(string function_name, string error_message) {
    string full_message = StringFormat("ERROR in %s: %s (Error: %d)", 
                                     function_name, error_message, GetLastError());
    LogMessage(full_message);
}

void LogDebug(string message) {
    #ifdef _DEBUG
    LogMessage("DEBUG: " + message);
    #endif
}

//-----------------------------------------------------------------------//
// Price and Point Conversion Functions
//-----------------------------------------------------------------------//
double PointsToPrice(double points) {
    return points * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
}

double PriceToPoints(double price) {
    return price / SymbolInfoDouble(_Symbol, SYMBOL_POINT);
}

int NormalizePrice(double &price) {
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    price = NormalizeDouble(price, digits);
    return digits;
}

//-----------------------------------------------------------------------//
// Object Management Functions
//-----------------------------------------------------------------------//
string GenerateObjectName(string type, int index) {
    return g_settings.object_prefix + type + "_" + IntegerToString(index) + "_" + IntegerToString(GetTickCount());
}

void CleanupOldObjects() {
    if(!g_settings.delete_old_objects) return;
    
    int total = ObjectsTotal(0, 0, -1);
    int deleted_count = 0;
    
    for(int i = total - 1; i >= 0; i--) {
        string name = ObjectName(0, i, 0, -1);
        if(StringFind(name, g_settings.object_prefix) == 0) {
            if(ObjectDelete(0, name)) {
                deleted_count++;
            }
        }
    }
    
    if(deleted_count > 0) {
        LogDebug(StringFormat("Cleaned up %d old objects", deleted_count));
    }
}

bool CreateObjectSafely(string name, ENUM_OBJECT type, int sub_window, 
                       datetime time1, double price1, 
                       datetime time2 = 0, double price2 = 0,
                       datetime time3 = 0, double price3 = 0) {
    // Delete existing object with same name
    if(ObjectFind(0, name) >= 0) {
        ObjectDelete(0, name);
    }
    
    bool result = false;
    switch(type) {
        case OBJ_RECTANGLE:
            result = ObjectCreate(0, name, type, sub_window, time1, price1, time2, price2);
            break;
        case OBJ_TREND:
            result = ObjectCreate(0, name, type, sub_window, time1, price1, time2, price2);
            break;
        case OBJ_ARROW_UP:
        case OBJ_ARROW_DOWN:
        case OBJ_TEXT:
            result = ObjectCreate(0, name, type, sub_window, time1, price1);
            break;
        default:
            result = ObjectCreate(0, name, type, sub_window, time1, price1, time2, price2);
            break;
    }
    
    if(!result) {
        LogError("CreateObjectSafely", StringFormat("Failed to create object %s", name));
    }
    
    return result;
}

//-----------------------------------------------------------------------//
// Color Utility Functions
//-----------------------------------------------------------------------//
color CreateTransparentColor(color base_color, int transparency) {
    // Ensure transparency is within valid range
    if(transparency < 0) transparency = 0;
    if(transparency > 255) transparency = 255;
    
    // Calculate alpha value (255 = opaque, 0 = transparent)
    int alpha = 255 - transparency;
    
    // Create ARGB color with transparency
    return (color)((alpha << 24) | (base_color & 0xFFFFFF));
}

color GetContrastColor(color background_color) {
    // Extract RGB components
    int red = (background_color >> 16) & 0xFF;
    int green = (background_color >> 8) & 0xFF;
    int blue = background_color & 0xFF;
    
    // Calculate luminance
    double luminance = (0.299 * red + 0.587 * green + 0.114 * blue) / 255.0;
    
    // Return black for light backgrounds, white for dark backgrounds
    return (luminance > 0.5) ? clrBlack : clrWhite;
}

//-----------------------------------------------------------------------//
// Time and Bar Utility Functions
//-----------------------------------------------------------------------//
bool IsNewBar() {
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(current_bar_time != last_bar_time) {
        last_bar_time = current_bar_time;
        return true;
    }
    return false;
}

int GetBarIndex(datetime time) {
    return iBarShift(_Symbol, PERIOD_CURRENT, time);
}

datetime GetBarTime(int index) {
    return iTime(_Symbol, PERIOD_CURRENT, index);
}

//-----------------------------------------------------------------------//
// Market Data Validation Functions
//-----------------------------------------------------------------------//
bool IsValidBarIndex(int bar_index) {
    int bars_total = iBars(_Symbol, PERIOD_CURRENT);
    return (bar_index >= 0 && bar_index < bars_total);
}

bool IsValidPrice(double price) {
    return (price > 0 && price != EMPTY_VALUE && !MathIsInf(price) && !MathIsNaN(price));
}

bool IsMarketOpen() {
    return SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_FULL;
}

//-----------------------------------------------------------------------//
// Configuration Functions
//-----------------------------------------------------------------------//
void InitializeSettings(const SAnalysisSettings &settings) {
    g_settings = settings;
    LogMessage("Settings initialized successfully");
}

SAnalysisSettings GetCurrentSettings() {
    return g_settings;
}

//-----------------------------------------------------------------------//
// Performance Monitoring Functions
//-----------------------------------------------------------------------//
uint StartTimer() {
    return GetTickCount();
}

void LogExecutionTime(string function_name, uint start_time) {
    uint elapsed = GetTickCount() - start_time;
    LogDebug(StringFormat("%s executed in %d ms", function_name, elapsed));
}

#endif // BEASTEU_UTILS_MQH
//+------------------------------------------------------------------+
