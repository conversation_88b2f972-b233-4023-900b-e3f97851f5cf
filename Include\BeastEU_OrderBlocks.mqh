//+------------------------------------------------------------------+
//|                                      BeastEU_OrderBlocks.mqh     |
//|                     Smart Money Concepts - Order Block Analysis  |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//+------------------------------------------------------------------+
#ifndef BEASTEU_ORDERBLOCKS_MQH
#define BEASTEU_ORDERBLOCKS_MQH

#include "BeastEU_Common.mqh"
#include "BeastEU_Utils.mqh"

//-----------------------------------------------------------------------//
// Order Block Detection Functions
//-----------------------------------------------------------------------//
bool IsValidOrderBlockCandle(int bar_index) {
    if(!IsValidBarIndex(bar_index)) {
        LogError("IsValidOrderBlockCandle", "Invalid bar index: " + IntegerToString(bar_index));
        return false;
    }
    
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, bar_index, 1, rates) <= 0) {
        LogError("IsValidOrderBlockCandle", "Failed to copy rates for bar " + IntegerToString(bar_index));
        return false;
    }
    
    double open = rates[0].open;
    double close = rates[0].close;
    double high = rates[0].high;
    double low = rates[0].low;
    
    // Validate price data
    if(!IsValidPrice(open) || !IsValidPrice(close) || !IsValidPrice(high) || !IsValidPrice(low)) {
        LogError("IsValidOrderBlockCandle", "Invalid price data for bar " + IntegerToString(bar_index));
        return false;
    }
    
    // Calculate candle properties
    double body_size = MathAbs(close - open);
    double candle_range = high - low;
    double point_value = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    // Check minimum size requirement
    if(candle_range < g_settings.ob_min_candle_size * point_value) {
        return false;
    }
    
    // Check body-to-range ratio for strong candles
    double body_ratio = (candle_range > 0) ? body_size / candle_range : 0;
    if(body_ratio < g_settings.ob_validation_ratio) {
        return false;
    }
    
    return true;
}

OrderBlock DetectOrderBlock(int start_bar) {
    OrderBlock ob = {};
    ob.is_valid = false;
    ob.is_mitigated = false;
    
    if(!IsValidBarIndex(start_bar) || !IsValidBarIndex(start_bar + 2)) {
        return ob;
    }
    
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, start_bar, 3, rates) < 3) {
        LogError("DetectOrderBlock", "Failed to copy 3 bars starting from " + IntegerToString(start_bar));
        return ob;
    }
    
    // Check for bullish order block pattern
    if(g_settings.show_bullish_ob) {
        bool down_candle = rates[2].close < rates[2].open;        // First candle is bearish
        bool strong_up = (rates[1].close > rates[1].open) && 
                        IsValidOrderBlockCandle(start_bar + 1);    // Second candle is strong bullish
        bool breaks_high = rates[1].close > rates[2].high;        // Breaks previous high
        
        if(down_candle && strong_up && breaks_high) {
            ob.time = rates[1].time;
            ob.high = rates[1].high;
            ob.low = rates[1].low;
            ob.open = rates[1].open;
            ob.close = rates[1].close;
            ob.is_bullish = true;
            ob.is_valid = true;
            ob.bar_index = start_bar + 1;
            ob.is_mitigated = false;
            
            LogDebug(StringFormat("Bullish Order Block detected at %.5f", (ob.high + ob.low) / 2.0));
            return ob;
        }
    }
    
    // Check for bearish order block pattern
    if(g_settings.show_bearish_ob) {
        bool up_candle = rates[2].close > rates[2].open;          // First candle is bullish
        bool strong_down = (rates[1].close < rates[1].open) && 
                          IsValidOrderBlockCandle(start_bar + 1);  // Second candle is strong bearish
        bool breaks_low = rates[1].close < rates[2].low;          // Breaks previous low
        
        if(up_candle && strong_down && breaks_low) {
            ob.time = rates[1].time;
            ob.high = rates[1].high;
            ob.low = rates[1].low;
            ob.open = rates[1].open;
            ob.close = rates[1].close;
            ob.is_bullish = false;
            ob.is_valid = true;
            ob.bar_index = start_bar + 1;
            ob.is_mitigated = false;
            
            LogDebug(StringFormat("Bearish Order Block detected at %.5f", (ob.high + ob.low) / 2.0));
        }
    }
    
    return ob;
}

bool IsOrderBlockMitigated(OrderBlock &ob) {
    if(!ob.is_valid || ob.is_mitigated) {
        return ob.is_mitigated;
    }
    
    // Get current price
    double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    if(ob.is_bullish) {
        // Bullish OB is mitigated when price returns to the low of the OB
        if(current_bid <= ob.low) {
            ob.is_mitigated = true;
            LogDebug(StringFormat("Bullish Order Block mitigated at %.5f", current_bid));
        }
    } else {
        // Bearish OB is mitigated when price returns to the high of the OB
        if(current_ask >= ob.high) {
            ob.is_mitigated = true;
            LogDebug(StringFormat("Bearish Order Block mitigated at %.5f", current_ask));
        }
    }
    
    return ob.is_mitigated;
}

void DrawOrderBlock(OrderBlock &ob, int index) {
    if(!ob.is_valid) return;
    
    uint start_time = StartTimer();
    
    string rect_name = GenerateObjectName("OB", index);
    color ob_color = ob.is_bullish ? g_config.bullish_ob_color : g_config.bearish_ob_color;
    
    // Adjust color transparency if order block is mitigated
    if(ob.is_mitigated) {
        ob_color = CreateTransparentColor(ob_color, 150);
    }
    
    // Create rectangle for order block
    datetime end_time = TimeCurrent() + PeriodSeconds(PERIOD_CURRENT) * 50;
    
    if(CreateObjectSafely(rect_name, OBJ_RECTANGLE, 0, ob.time, ob.high, end_time, ob.low)) {
        // Set rectangle properties
        ObjectSetInteger(0, rect_name, OBJPROP_COLOR, ob_color);
        ObjectSetInteger(0, rect_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, rect_name, OBJPROP_WIDTH, g_config.ob_rect_width);
        ObjectSetInteger(0, rect_name, OBJPROP_BACK, true);
        ObjectSetInteger(0, rect_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, rect_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, rect_name, OBJPROP_HIDDEN, false);
        
        // Add tooltip text
        string tooltip = StringFormat("%s Order Block\\nTime: %s\\nHigh: %.5f\\nLow: %.5f\\nStatus: %s",
                                    ob.is_bullish ? "Bullish" : "Bearish",
                                    TimeToString(ob.time, TIME_DATE|TIME_MINUTES),
                                    ob.high, ob.low,
                                    ob.is_mitigated ? "Mitigated" : "Active");
        ObjectSetString(0, rect_name, OBJPROP_TOOLTIP, tooltip);
        
        // Add text label
        string label_name = GenerateObjectName("OB_Label", index);
        double label_price = (ob.high + ob.low) / 2.0;
        
        if(CreateObjectSafely(label_name, OBJ_TEXT, 0, ob.time, label_price)) {
            string label_text = ob.is_bullish ? "Bull OB" : "Bear OB";
            if(ob.is_mitigated) label_text += " (M)";
            
            ObjectSetString(0, label_name, OBJPROP_TEXT, label_text);
            ObjectSetInteger(0, label_name, OBJPROP_COLOR, GetContrastColor(ob_color));
            ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 8);
            ObjectSetString(0, label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, label_name, OBJPROP_SELECTABLE, false);
            ObjectSetInteger(0, label_name, OBJPROP_HIDDEN, false);
            ObjectSetString(0, label_name, OBJPROP_TOOLTIP, tooltip);
        }
        
        g_total_objects += 2; // Rectangle + Label
    }
    
    LogExecutionTime("DrawOrderBlock", start_time);
}

//-----------------------------------------------------------------------//
// Order Block Analysis Functions
//-----------------------------------------------------------------------//
void AnalyzeOrderBlocks() {
    if(!g_config.show_bullish_ob && !g_config.show_bearish_ob) {
        return;
    }
    
    uint start_time = StartTimer();
    int object_counter = 0;
    int detected_count = 0;
    
    LogDebug("Starting Order Block analysis...");
    
    // Analyze bars for order block patterns
    for(int i = 2; i < g_config.ob_lookback_bars && i < iBars(_Symbol, PERIOD_CURRENT) - 3; i++) {
        OrderBlock ob = DetectOrderBlock(i);
        if(ob.is_valid) {
            // Check if order block is mitigated
            IsOrderBlockMitigated(ob);
            
            // Draw the order block
            DrawOrderBlock(ob, object_counter++);
            detected_count++;
            
            Log(StringFormat("Order Block #%d: %s at %.5f-%.5f (%s)", 
                           detected_count,
                           ob.is_bullish ? "Bullish" : "Bearish", 
                           ob.low, ob.high,
                           ob.is_mitigated ? "Mitigated" : "Active"));
        }
    }
    
    LogExecutionTime("AnalyzeOrderBlocks", start_time);
    Log(StringFormat("Order Block analysis complete: %d blocks detected", detected_count));
}

//-----------------------------------------------------------------------//
// Order Block Utility Functions
//-----------------------------------------------------------------------//
double GetOrderBlockPriorityScore(OrderBlock &ob) {
    if(!ob.is_valid) return 0.0;
    
    double score = 0.0;
    
    // Higher score for larger order blocks
    double size = ob.high - ob.low;
    score += PRICE_TO_POINTS(size) * 0.1;
    
    // Higher score for more recent order blocks
    int bars_ago = iBarShift(_Symbol, PERIOD_CURRENT, ob.time);
    if(bars_ago >= 0) {
        score += MathMax(0, 100 - bars_ago);
    }
    
    // Lower score for mitigated order blocks
    if(ob.is_mitigated) {
        score *= 0.3;
    }
    
    // Higher score for order blocks that respect market structure
    double body_size = MathAbs(ob.close - ob.open);
    double range = ob.high - ob.low;
    if(range > 0) {
        double body_ratio = body_size / range;
        score += body_ratio * 50;
    }
    
    return score;
}

bool IsCurrentPriceInOrderBlock(OrderBlock &ob, double tolerance_points = 5.0) {
    if(!ob.is_valid || ob.is_mitigated) return false;
    
    double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2.0;
    double tolerance = PointsToPrice(tolerance_points);
    
    return (current_price >= (ob.low - tolerance) && current_price <= (ob.high + tolerance));
}

int CountActiveOrderBlocks() {
    // This function would maintain a global array of detected order blocks
    // For now, we'll return the object count as a proxy
    return g_total_objects / 2; // Approximate since each OB creates 2 objects
}

#endif // BEASTEU_ORDERBLOCKS_MQH
//+------------------------------------------------------------------+
