//+------------------------------------------------------------------+
//|                                                      BeastEU.mq5 |
//|                     Smart Money Concepts & Order Blocks EA v2.0  |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "Modular Smart Money Concepts Expert Advisor"
#property description "Analyzes Order Blocks, Fair Value Gaps, Market Structure,"
#property description "Liquidity Zones, and Premium/Discount Areas"

//+------------------------------------------------------------------+
//| Include Files                                                    |
//+------------------------------------------------------------------+
#include "Include/BeastEU_Analyzer.mqh"

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
//--- Order Block Detection Settings
input group "=== ORDER BLOCK SETTINGS ==="
//--- Order Block Detection Settings
input group "=== ORDER BLOCK SETTINGS ==="
input int    OB_LookbackBars        = 20;     // Bars to look back for order blocks
input int    OB_MinCandleSize       = 10;     // Minimum candle size in points
input double OB_ValidationRatio     = 0.6;    // Body-to-wick ratio for valid OB
input bool   ShowBullishOB          = true;   // Show bullish order blocks
input bool   ShowBearishOB          = true;   // Show bearish order blocks
input color  BullishOB_Color        = clrLimeGreen;  // Bullish OB color
input color  BearishOB_Color        = clrRed;        // Bearish OB color
input int    OB_RectWidth           = 2;      // Order block rectangle width

//--- Fair Value Gap (Imbalance) Settings
input group "=== FAIR VALUE GAP SETTINGS ==="
input bool   ShowFVG                = true;   // Show Fair Value Gaps
input int    FVG_MinSize            = 5;      // Minimum FVG size in points
input color  BullishFVG_Color       = clrDodgerBlue;   // Bullish FVG color
input color  BearishFVG_Color       = clrOrange;       // Bearish FVG color
input int    FVG_Transparency       = 80;     // FVG transparency (0-255)

//--- Market Structure Settings
input group "=== MARKET STRUCTURE SETTINGS ==="
input bool   ShowMarketStructure    = true;   // Show market structure
input int    MS_SwingStrength       = 5;      // Swing point strength
input color  StructureHigh_Color    = clrYellow;      // Structure high color
input color  StructureLow_Color     = clrCyan;        // Structure low color
input int    MS_LineWidth           = 2;      // Market structure line width

//--- Liquidity Zones Settings
input group "=== LIQUIDITY ZONES SETTINGS ==="
input bool   ShowLiquidityZones     = true;   // Show liquidity zones
input int    LQ_LookbackBars        = 50;     // Bars to scan for liquidity
input color  LiquidityZone_Color    = clrMagenta;     // Liquidity zone color
input int    LQ_MinTouches          = 3;      // Minimum touches for liquidity zone

//--- Premium/Discount Zones Settings
input group "=== PREMIUM/DISCOUNT SETTINGS ==="
input bool   ShowPremiumDiscount    = true;   // Show premium/discount zones
input int    PD_LookbackPeriod      = 100;    // Period for premium/discount calculation
input color  PremiumZone_Color      = clrCrimson;     // Premium zone color
input color  DiscountZone_Color     = clrForestGreen; // Discount zone color

//--- Analysis Settings
input group "=== ANALYSIS SETTINGS ==="
input int    UpdateFrequency        = 1;      // Analysis update frequency (bars)
input bool   EnablePerformanceMode = false;   // Enable performance optimization
input int    MaxOrderBlocks        = 50;      // Maximum order blocks to display
input int    MaxFairValueGaps      = 30;      // Maximum FVGs to display

//--- Display Settings
input group "=== DISPLAY SETTINGS ==="
input bool   EnableLogging          = true;   // Enable detailed logging
input string ObjectPrefix          = "SMC_";  // Object prefix
input bool   DeleteOldObjects      = true;   // Delete old objects on refresh
input bool   ShowAnalysisInfo      = true;   // Show analysis information panel
input int    InfoPanelCorner       = 1;      // Info panel corner (0-3)

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CSmartMoneyAnalyzer* g_Analyzer = NULL;
datetime LastUpdate = 0;
int LastAnalyzedBar = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Create the analyzer instance
    g_Analyzer = new CSmartMoneyAnalyzer();
    
    if(g_Analyzer == NULL) {
        Print("ERROR: Failed to create Smart Money Analyzer");
        return INIT_FAILED;
    }
    
    // Initialize with current settings
    SAnalysisSettings settings;
    settings.ob_lookback_bars = OB_LookbackBars;
    settings.ob_min_candle_size = OB_MinCandleSize;
    settings.ob_validation_ratio = OB_ValidationRatio;
    settings.show_bullish_ob = ShowBullishOB;
    settings.show_bearish_ob = ShowBearishOB;
    settings.bullish_ob_color = BullishOB_Color;
    settings.bearish_ob_color = BearishOB_Color;
    settings.ob_rect_width = OB_RectWidth;
    
    settings.show_fvg = ShowFVG;
    settings.fvg_min_size = FVG_MinSize;
    settings.bullish_fvg_color = BullishFVG_Color;
    settings.bearish_fvg_color = BearishFVG_Color;
    settings.fvg_transparency = FVG_Transparency;
    
    settings.show_market_structure = ShowMarketStructure;
    settings.ms_swing_strength = MS_SwingStrength;
    settings.structure_high_color = StructureHigh_Color;
    settings.structure_low_color = StructureLow_Color;
    settings.ms_line_width = MS_LineWidth;
    
    settings.show_liquidity_zones = ShowLiquidityZones;
    settings.lq_lookback_bars = LQ_LookbackBars;
    settings.liquidity_zone_color = LiquidityZone_Color;
    settings.lq_min_touches = LQ_MinTouches;
    
    settings.show_premium_discount = ShowPremiumDiscount;
    settings.pd_lookback_period = PD_LookbackPeriod;
    settings.premium_zone_color = PremiumZone_Color;
    settings.discount_zone_color = DiscountZone_Color;
    
    settings.enable_logging = EnableLogging;
    settings.object_prefix = ObjectPrefix;
    settings.delete_old_objects = DeleteOldObjects;
    settings.update_frequency = UpdateFrequency;
    settings.enable_performance_mode = EnablePerformanceMode;
    settings.max_order_blocks = MaxOrderBlocks;
    settings.max_fair_value_gaps = MaxFairValueGaps;
    settings.show_analysis_info = ShowAnalysisInfo;
    settings.info_panel_corner = InfoPanelCorner;
    
    // Initialize the analyzer
    if(!g_Analyzer.Initialize(settings)) {
        Print("ERROR: Failed to initialize Smart Money Analyzer");
        delete g_Analyzer;
        g_Analyzer = NULL;
        return INIT_FAILED;
    }
    
    // Perform initial analysis
    g_Analyzer.AnalyzeMarket();
    LastUpdate = TimeCurrent();
    LastAnalyzedBar = Bars(_Symbol, PERIOD_CURRENT);
    
    LogMessage("BeastEU Smart Money Concepts EA v2.0 Initialized Successfully");
    LogMessage("Modular architecture with enhanced analysis capabilities");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    string reason_text = "";
    switch(reason) {
        case REASON_PROGRAM:        reason_text = "Expert removed"; break;
        case REASON_REMOVE:         reason_text = "Expert removed from chart"; break;
        case REASON_RECOMPILE:      reason_text = "Expert recompiled"; break;
        case REASON_CHARTCHANGE:    reason_text = "Chart period/symbol changed"; break;
        case REASON_CHARTCLOSE:     reason_text = "Chart closed"; break;
        case REASON_PARAMETERS:     reason_text = "Input parameters changed"; break;
        case REASON_ACCOUNT:        reason_text = "Account changed"; break;
        case REASON_TEMPLATE:       reason_text = "Template changed"; break;
        case REASON_INITFAILED:     reason_text = "Initialization failed"; break;
        case REASON_CLOSE:          reason_text = "Terminal closed"; break;
        default:                    reason_text = "Unknown reason"; break;
    }
    
    LogMessage("BeastEU EA Shutdown: " + reason_text);
    
    if(g_Analyzer != NULL) {
        g_Analyzer.Cleanup();
        delete g_Analyzer;
        g_Analyzer = NULL;
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    if(g_Analyzer == NULL) return;
    
    // Check if we need to update analysis
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    int current_bars = Bars(_Symbol, PERIOD_CURRENT);
    
    bool should_update = false;
    
    // Update on new bar
    if(current_bar_time != last_bar_time) {
        should_update = true;
        last_bar_time = current_bar_time;
    }
    
    // Update based on frequency setting
    if(current_bars - LastAnalyzedBar >= UpdateFrequency) {
        should_update = true;
    }
    
    if(should_update) {
        g_Analyzer.AnalyzeMarket();
        LastAnalyzedBar = current_bars;
        LastUpdate = TimeCurrent();
    }
    
    // Update real-time components
    g_Analyzer.UpdateRealTime();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer() {
    if(g_Analyzer != NULL) {
        g_Analyzer.OnTimer();
    }
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam) {
    if(g_Analyzer != NULL) {
        g_Analyzer.OnChartEvent(id, lparam, dparam, sparam);
    }
}

//+------------------------------------------------------------------+
//| Trade function                                                   |
//+------------------------------------------------------------------+
void OnTrade() {
    if(g_Analyzer != NULL) {
        g_Analyzer.OnTrade();
    }
}

//+------------------------------------------------------------------+
//| Book event function                                              |
//+------------------------------------------------------------------+
void OnBookEvent(const string &symbol) {
    if(g_Analyzer != NULL && symbol == _Symbol) {
        g_Analyzer.OnBookEvent(symbol);
    }
}

//+------------------------------------------------------------------+
//| Utility function for logging                                     |
//+------------------------------------------------------------------+
void LogMessage(string message) {
    if(EnableLogging) {
        string timestamp = TimeToString(TimeTradeServer(), TIME_DATE|TIME_SECONDS);
        Print("[", timestamp, "] BeastEU: ", message);
    }
}

//+------------------------------------------------------------------+